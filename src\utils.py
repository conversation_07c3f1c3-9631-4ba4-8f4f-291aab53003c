"""Utility functions for Valorant Tool."""

import re
from typing import <PERSON>ple, Optional
from rich.console import Console
from rich.table import Table
from rich.panel import Panel
from rich.text import Text
from rich import box

console = Console()


def parse_riot_id(riot_id: str) -> Tuple[str, str]:
    """
    Parse a Riot ID into name and tag components.
    
    Args:
        riot_id: Riot ID in format "name#tag"
        
    Returns:
        Tuple of (name, tag)
        
    Raises:
        ValueError: If the format is invalid
    """
    if '#' not in riot_id:
        raise ValueError("Invalid Riot ID format. Use 'name#tag' format.")
    
    parts = riot_id.split('#')
    if len(parts) != 2:
        raise ValueError("Invalid Riot ID format. Use 'name#tag' format.")
    
    name, tag = parts
    if not name.strip() or not tag.strip():
        raise ValueError("Name and tag cannot be empty.")
    
    return name.strip(), tag.strip()


def validate_region(region: str) -> str:
    """
    Validate and normalize region code.
    
    Args:
        region: Region code
        
    Returns:
        Normalized region code
        
    Raises:
        ValueError: If region is invalid
    """
    valid_regions = ['eu', 'na', 'ap', 'kr', 'latam', 'br']
    region = region.lower().strip()
    
    if region not in valid_regions:
        raise ValueError(f"Invalid region: {region}. Valid regions: {', '.join(valid_regions)}")
    
    return region


def validate_platform(platform: str) -> str:
    """
    Validate and normalize platform.
    
    Args:
        platform: Platform name
        
    Returns:
        Normalized platform name
        
    Raises:
        ValueError: If platform is invalid
    """
    valid_platforms = ['pc', 'console']
    platform = platform.lower().strip()
    
    if platform not in valid_platforms:
        raise ValueError(f"Invalid platform: {platform}. Valid platforms: {', '.join(valid_platforms)}")
    
    return platform


def get_rank_color(tier_name: str) -> str:
    """
    Get color for rank display based on tier name.
    
    Args:
        tier_name: Rank tier name
        
    Returns:
        Rich color string
    """
    tier_colors = {
        'Unrated': 'white',
        'Iron': 'dim white',
        'Bronze': '#CD7F32',
        'Silver': '#C0C0C0',
        'Gold': '#FFD700',
        'Platinum': '#00CED1',
        'Diamond': '#B9F2FF',
        'Ascendant': '#00FF87',
        'Immortal': '#FF6B9D',
        'Radiant': '#FFFF99'
    }
    
    # Extract base rank name (remove numbers)
    base_rank = re.sub(r'\s+\d+$', '', tier_name)
    return tier_colors.get(base_rank, 'white')


def print_error(message: str):
    """Print an error message with formatting."""
    console.print(f"[bold red]Error:[/bold red] {message}")


def print_success(message: str):
    """Print a success message with formatting."""
    console.print(f"[bold green]Success:[/bold green] {message}")


def print_info(message: str):
    """Print an info message with formatting."""
    console.print(f"[bold blue]Info:[/bold blue] {message}")


def print_warning(message: str):
    """Print a warning message with formatting."""
    console.print(f"[bold yellow]Warning:[/bold yellow] {message}")


def create_player_table(players, title: str = "Players") -> Table:
    """
    Create a formatted table for displaying players.
    
    Args:
        players: List of Player objects
        title: Table title
        
    Returns:
        Rich Table object
    """
    table = Table(title=title, box=box.ROUNDED)
    table.add_column("Riot ID", style="cyan", no_wrap=True)
    table.add_column("Current Rank", style="bold")
    table.add_column("Peak Rank", style="dim")
    table.add_column("Level", justify="center")
    table.add_column("Platform", justify="center")
    
    for player in players:
        rank_color = get_rank_color(player.current_rank.current_tier_name if player.current_rank else "Unrated")
        
        table.add_row(
            player.riot_id,
            Text(player.display_rank, style=rank_color),
            player.display_peak,
            str(player.account_level),
            player.platform.upper()
        )
    
    return table
