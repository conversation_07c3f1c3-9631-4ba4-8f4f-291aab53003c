{% extends "base.html" %}

{% block title %}{{ match.map_name }} Match - Valorant Rank Checker{% endblock %}

{% block content %}
<div class="row mb-4">
    <div class="col-lg-12">
        <div class="card">
            <div class="card-header">
                <div class="row align-items-center">
                    <div class="col-md-8">
                        <h3 class="card-title mb-0">
                            <i class="bi bi-map"></i> {{ match.map_name }}
                        </h3>
                        <small class="text-muted">{{ match.game_mode }} • {{ match.started_at.strftime('%Y-%m-%d %H:%M UTC') }}</small>
                    </div>
                    <div class="col-md-4 text-md-end">
                        <h2 class="mb-0">
                            <span class="{{ 'text-success' if match.red_team_won else 'text-danger' }}">{{ match.red_rounds_won }}</span>
                            <span class="text-muted">-</span>
                            <span class="{{ 'text-success' if not match.red_team_won else 'text-danger' }}">{{ match.blue_rounds_won }}</span>
                        </h2>
                        <small class="text-muted">{{ match.game_length_minutes }} minutes</small>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <!-- Red Team -->
    <div class="col-lg-6 mb-4">
        <div class="card team-red {{ 'win' if match.red_team_won else 'loss' }}">
            <div class="card-header">
                <h4 class="card-title mb-0">
                    <i class="bi bi-circle-fill" style="color: var(--valorant-red);"></i> 
                    Red Team {{ '(Winner)' if match.red_team_won else '(Loser)' }}
                </h4>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-dark table-sm">
                        <thead>
                            <tr>
                                <th>Player</th>
                                <th>Agent</th>
                                <th>Current Rank</th>
                                <th>Peak Rank</th>
                                <th class="text-center">K/D/A</th>
                                <th class="text-center">Score</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for player in match.red_team %}
                            <tr>
                                <td>
                                    <a href="{{ url_for('player_profile', name=player.name, tag=player.tag) }}" 
                                       class="text-decoration-none text-light">
                                        {{ player.riot_id }}
                                    </a>
                                    <br>
                                    <small class="text-muted">Lvl {{ player.account_level }}</small>
                                </td>
                                <td>{{ player.agent_name }}</td>
                                <td>
                                    {% set rank_class = 'rank-' + player.tier_name.lower().split()[0] %}
                                    <span class="{{ rank_class }}">{{ player.display_rank }}</span>
                                </td>
                                <td>
                                    {% if player.peak_rank %}
                                        {% set peak_rank_class = 'rank-' + player.peak_rank.tier_name.lower().split()[0] %}
                                        <span class="{{ peak_rank_class }}">{{ player.display_peak }}</span>
                                    {% else %}
                                        <span class="text-muted">No Peak Data</span>
                                    {% endif %}
                                </td>
                                <td class="text-center">
                                    <strong>{{ player.kills }}</strong>/{{ player.deaths }}/<strong>{{ player.assists }}</strong>
                                </td>
                                <td class="text-center">{{ player.score }}</td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Blue Team -->
    <div class="col-lg-6 mb-4">
        <div class="card team-blue {{ 'win' if not match.red_team_won else 'loss' }}">
            <div class="card-header">
                <h4 class="card-title mb-0">
                    <i class="bi bi-circle-fill" style="color: var(--valorant-blue);"></i> 
                    Blue Team {{ '(Winner)' if not match.red_team_won else '(Loser)' }}
                </h4>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-dark table-sm">
                        <thead>
                            <tr>
                                <th>Player</th>
                                <th>Agent</th>
                                <th>Current Rank</th>
                                <th>Peak Rank</th>
                                <th class="text-center">K/D/A</th>
                                <th class="text-center">Score</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for player in match.blue_team %}
                            <tr>
                                <td>
                                    <a href="{{ url_for('player_profile', name=player.name, tag=player.tag) }}" 
                                       class="text-decoration-none text-light">
                                        {{ player.riot_id }}
                                    </a>
                                    <br>
                                    <small class="text-muted">Lvl {{ player.account_level }}</small>
                                </td>
                                <td>{{ player.agent_name }}</td>
                                <td>
                                    {% set rank_class = 'rank-' + player.tier_name.lower().split()[0] %}
                                    <span class="{{ rank_class }}">{{ player.display_rank }}</span>
                                </td>
                                <td>
                                    {% if player.peak_rank %}
                                        {% set peak_rank_class = 'rank-' + player.peak_rank.tier_name.lower().split()[0] %}
                                        <span class="{{ peak_rank_class }}">{{ player.display_peak }}</span>
                                    {% else %}
                                        <span class="text-muted">No Peak Data</span>
                                    {% endif %}
                                </td>
                                <td class="text-center">
                                    <strong>{{ player.kills }}</strong>/{{ player.deaths }}/<strong>{{ player.assists }}</strong>
                                </td>
                                <td class="text-center">{{ player.score }}</td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Match Statistics -->
<div class="row">
    <div class="col-lg-12">
        <div class="card">
            <div class="card-header">
                <h4 class="card-title mb-0">
                    <i class="bi bi-bar-chart"></i> Match Statistics
                </h4>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-3 text-center">
                        <h5>Total Kills</h5>
                        <h3 class="text-primary">
                            {{ match.all_players | sum(attribute='kills') }}
                        </h3>
                    </div>
                    <div class="col-md-3 text-center">
                        <h5>Average Score</h5>
                        <h3 class="text-info">
                            {{ (match.all_players | sum(attribute='score') / match.all_players | length) | round | int }}
                        </h3>
                    </div>
                    <div class="col-md-3 text-center">
                        <h5>Platform</h5>
                        <h3 class="text-success">{{ match.platform.upper() }}</h3>
                    </div>
                    <div class="col-md-3 text-center">
                        <h5>Region</h5>
                        <h3 class="text-warning">{{ match.region.upper() }}</h3>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="text-center mt-4">
    <button onclick="history.back()" class="btn btn-outline-secondary">
        <i class="bi bi-arrow-left"></i> Go Back
    </button>
    <a href="{{ url_for('index') }}" class="btn btn-outline-primary">
        <i class="bi bi-house"></i> Home
    </a>
</div>
{% endblock %}

{% block extra_js %}
<script>
// Add click handlers for player links
document.addEventListener('DOMContentLoaded', function() {
    // Highlight rows on hover
    const tableRows = document.querySelectorAll('tbody tr');
    tableRows.forEach(row => {
        row.addEventListener('mouseenter', function() {
            this.style.backgroundColor = 'rgba(255, 255, 255, 0.1)';
        });
        row.addEventListener('mouseleave', function() {
            this.style.backgroundColor = '';
        });
    });
});
</script>
{% endblock %}
