{% extends "base.html" %}

{% block title %}{{ error }} - Valorant Rank Checker{% endblock %}

{% block content %}
<div class="row justify-content-center">
    <div class="col-lg-8">
        <div class="card">
            <div class="card-body text-center py-5">
                <i class="bi bi-exclamation-triangle display-1 text-warning mb-4"></i>
                
                <h2 class="text-danger mb-3">{{ error }}</h2>
                
                <p class="lead text-muted mb-4">{{ message }}</p>
                
                <div class="d-flex justify-content-center gap-3">
                    <a href="{{ url_for('index') }}" class="btn btn-primary">
                        <i class="bi bi-house"></i> Go Home
                    </a>
                    <button onclick="history.back()" class="btn btn-outline-secondary">
                        <i class="bi bi-arrow-left"></i> Go Back
                    </button>
                </div>
            </div>
        </div>
        
        {% if error == "Rate Limit Exceeded" %}
        <div class="alert alert-info mt-4" role="alert">
            <i class="bi bi-info-circle"></i>
            <strong>Rate Limit Info:</strong> 
            The API has usage limits to ensure fair access for all users. 
            Please wait a moment before making another request.
        </div>
        {% elif error == "Player not found" %}
        <div class="alert alert-info mt-4" role="alert">
            <i class="bi bi-lightbulb"></i>
            <strong>Tips:</strong>
            <ul class="mb-0 mt-2">
                <li>Make sure the Riot ID is spelled correctly</li>
                <li>Use the format: name#tag (e.g., TenZ#SEN)</li>
                <li>The player must have played Valorant recently</li>
                <li>Try a different region if the player might be from another region</li>
            </ul>
        </div>
        {% endif %}
    </div>
</div>
{% endblock %}
