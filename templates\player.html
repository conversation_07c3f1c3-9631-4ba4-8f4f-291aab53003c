{% extends "base.html" %}

{% block title %}{{ player.riot_id }} - Valorant Profile{% endblock %}

{% block content %}
<div class="row">
    <div class="col-lg-4 mb-4">
        <!-- Player Info Card -->
        <div class="card">
            <div class="card-header">
                <h4 class="card-title mb-0">
                    <i class="bi bi-person-circle"></i> {{ player.riot_id }}
                </h4>
            </div>
            <div class="card-body">
                <div class="row mb-3">
                    <div class="col-6">
                        <strong>Account Level:</strong>
                    </div>
                    <div class="col-6">
                        <span class="badge bg-secondary">{{ player.account_level }}</span>
                    </div>
                </div>
                
                <div class="row mb-3">
                    <div class="col-6">
                        <strong>Platform:</strong>
                    </div>
                    <div class="col-6">
                        <span class="badge bg-info">{{ player.platform.upper() }}</span>
                    </div>
                </div>
                
                <div class="row mb-3">
                    <div class="col-6">
                        <strong>Region:</strong>
                    </div>
                    <div class="col-6">
                        <span class="badge bg-primary">{{ player.region.upper() }}</span>
                    </div>
                </div>
                
                <hr>
                
                <div class="text-center">
                    <h5>Current Rank</h5>
                    {% if player.current_rank %}
                        {% set rank_class = 'rank-' + player.current_rank.current_tier_name.lower().split()[0] %}
                        <h3 class="{{ rank_class }}">
                            <i class="bi bi-trophy"></i> {{ player.current_rank.current_tier_name }}
                        </h3>
                        {% if player.current_rank.rr > 0 %}
                            <p class="text-muted">{{ player.current_rank.rr }} RR</p>
                        {% endif %}
                        {% if player.current_rank.leaderboard_rank %}
                            <p class="text-warning">
                                <i class="bi bi-star"></i> Leaderboard #{{ player.current_rank.leaderboard_rank }}
                            </p>
                        {% endif %}
                    {% else %}
                        <h3 class="text-muted">
                            <i class="bi bi-question-circle"></i> No Competitive Data
                        </h3>
                    {% endif %}
                </div>
                
                <hr>
                
                <div class="text-center">
                    <h5>Peak Rank</h5>
                    {% if player.peak_rank %}
                        {% set peak_rank_class = 'rank-' + player.peak_rank.tier_name.lower().split()[0] %}
                        <h4 class="{{ peak_rank_class }}">
                            <i class="bi bi-award"></i> {{ player.peak_rank.tier_name }}
                        </h4>
                        <p class="text-muted">Season {{ player.peak_rank.season }}</p>
                    {% else %}
                        <h4 class="text-muted">
                            <i class="bi bi-question-circle"></i> No Competitive Data
                        </h4>
                    {% endif %}
                </div>
                
                {% if player.updated_at %}
                <hr>
                <div class="text-center">
                    <small class="text-muted">
                        <i class="bi bi-clock"></i> 
                        Last updated: {{ player.updated_at.strftime('%Y-%m-%d %H:%M UTC') }}
                    </small>
                </div>
                {% endif %}
            </div>
        </div>
        
        <!-- Quick Actions -->
        <div class="card mt-3">
            <div class="card-body">
                <h6 class="card-title">Quick Actions</h6>
                <div class="d-grid gap-2">
                    <button class="btn btn-outline-primary btn-sm" onclick="copyProfileLink()">
                        <i class="bi bi-link"></i> Copy Profile Link
                    </button>
                    <a href="{{ url_for('index') }}" class="btn btn-outline-secondary btn-sm">
                        <i class="bi bi-search"></i> Look Up Another Player
                    </a>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-lg-8">
        <!-- Match History -->
        <div class="card">
            <div class="card-header">
                <h4 class="card-title mb-0">
                    <i class="bi bi-clock-history"></i> Recent Matches
                </h4>
            </div>
            <div class="card-body">
                {% if matches %}
                    <div class="row">
                        {% for match_data in matches %}
                            {% set match = match_data.match_info %}
                            {% set player_data = match_data.player_data %}
                            {% set match_id = match_data.match_id %}
                            
                            {% set is_win = (player_data.team_id == 'Red' and match.red_team_won) or (player_data.team_id == 'Blue' and not match.red_team_won) %}
                            
                            <div class="col-md-6 mb-3">
                                <div class="card match-card {{ 'win' if is_win else 'loss' }} {{ 'team-red' if player_data.team_id == 'Red' else 'team-blue' }}">
                                    <div class="card-body">
                                        <div class="d-flex justify-content-between align-items-start mb-2">
                                            <div>
                                                <h6 class="card-title mb-1">{{ match.map_name }}</h6>
                                                <small class="text-muted">{{ match.game_mode }}</small>
                                            </div>
                                            <span class="badge {{ 'bg-success' if is_win else 'bg-danger' }}">
                                                {{ 'WIN' if is_win else 'LOSS' }}
                                            </span>
                                        </div>
                                        
                                        <div class="row text-center mb-2">
                                            <div class="col-4">
                                                <strong>{{ player_data.kills }}</strong>
                                                <br><small class="text-muted">K</small>
                                            </div>
                                            <div class="col-4">
                                                <strong>{{ player_data.deaths }}</strong>
                                                <br><small class="text-muted">D</small>
                                            </div>
                                            <div class="col-4">
                                                <strong>{{ player_data.assists }}</strong>
                                                <br><small class="text-muted">A</small>
                                            </div>
                                        </div>
                                        
                                        <div class="row text-center mb-2">
                                            <div class="col-6">
                                                <strong>{{ match.score_display }}</strong>
                                                <br><small class="text-muted">Score</small>
                                            </div>
                                            <div class="col-6">
                                                <strong>{{ player_data.agent_name }}</strong>
                                                <br><small class="text-muted">Agent</small>
                                            </div>
                                        </div>
                                        
                                        <div class="text-center">
                                            <small class="text-muted">
                                                <i class="bi bi-clock"></i> 
                                                {{ match.started_at.strftime('%m/%d %H:%M') }}
                                            </small>
                                        </div>
                                        
                                        <div class="text-center mt-2">
                                            <a href="{{ url_for('match_details', region=match.region, match_id=match_id) }}" 
                                               class="btn btn-outline-primary btn-sm">
                                                <i class="bi bi-eye"></i> View Match
                                            </a>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        {% endfor %}
                    </div>
                    
                    {% if matches|length == 5 %}
                    <div class="alert alert-info mt-3" role="alert">
                        <i class="bi bi-info-circle"></i>
                        Showing recent 5 matches. More match history features coming soon!
                    </div>
                    {% endif %}
                {% else %}
                    <div class="text-center py-5">
                        <i class="bi bi-clock-history display-4 text-muted mb-3"></i>
                        <h5 class="text-muted">No Recent Matches Found</h5>
                        <p class="text-muted">
                            This player may not have played recently or their match history is private.
                        </p>
                    </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
function copyProfileLink() {
    const url = window.location.href;
    navigator.clipboard.writeText(url).then(function() {
        // Show success message
        const btn = event.target.closest('button');
        const originalText = btn.innerHTML;
        btn.innerHTML = '<i class="bi bi-check"></i> Copied!';
        btn.classList.remove('btn-outline-primary');
        btn.classList.add('btn-success');
        
        setTimeout(function() {
            btn.innerHTML = originalText;
            btn.classList.remove('btn-success');
            btn.classList.add('btn-outline-primary');
        }, 2000);
    }).catch(function(err) {
        console.error('Could not copy text: ', err);
        alert('Could not copy link. Please copy manually: ' + url);
    });
}
</script>
{% endblock %}
