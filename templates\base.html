<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}Valorant Rank Checker{% endblock %}</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Bootstrap Icons -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    
    <style>
        :root {
            --valorant-red: #ff4655;
            --valorant-blue: #0084ff;
            --valorant-accent: #00d4ff;
            --bg-primary: #f8f9fa;
            --bg-secondary: #ffffff;
            --text-primary: #212529;
            --text-secondary: #6c757d;
            --border-color: #dee2e6;
        }

        body {
            background: linear-gradient(135deg, var(--bg-primary) 0%, #e9ecef 100%);
            color: var(--text-primary);
            min-height: 100vh;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }

        .navbar {
            background: var(--bg-secondary) !important;
            border-bottom: 2px solid var(--valorant-red);
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        .navbar-brand {
            color: var(--valorant-red) !important;
            font-weight: bold;
            font-size: 1.5rem;
        }

        .navbar-nav .nav-link {
            color: var(--text-primary) !important;
            transition: color 0.3s ease;
            font-weight: 500;
        }

        .navbar-nav .nav-link:hover {
            color: var(--valorant-blue) !important;
        }

        .card {
            background: var(--bg-secondary);
            border: 1px solid var(--border-color);
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }

        .card-header {
            background: rgba(255, 70, 85, 0.05);
            border-bottom: 1px solid var(--border-color);
            color: var(--text-primary);
        }
        
        .btn-primary {
            background: var(--valorant-red);
            border-color: var(--valorant-red);
            transition: all 0.3s ease;
        }
        
        .btn-primary:hover {
            background: #e63946;
            border-color: #e63946;
            transform: translateY(-2px);
        }
        
        .btn-outline-primary {
            color: var(--valorant-blue);
            border-color: var(--valorant-blue);
        }

        .btn-outline-primary:hover {
            background: var(--valorant-blue);
            border-color: var(--valorant-blue);
            color: white;
        }

        .form-control {
            background: var(--bg-secondary);
            border: 1px solid var(--border-color);
            color: var(--text-primary);
        }

        .form-control:focus {
            background: var(--bg-secondary);
            border-color: var(--valorant-blue);
            box-shadow: 0 0 0 0.2rem rgba(0, 132, 255, 0.25);
            color: var(--text-primary);
        }

        .form-control::placeholder {
            color: var(--text-secondary);
        }
        
        .rank-iron { color: #6c757d; font-weight: 600; }
        .rank-bronze { color: #8b4513; font-weight: 600; }
        .rank-silver { color: #708090; font-weight: 600; }
        .rank-gold { color: #b8860b; font-weight: 600; }
        .rank-platinum { color: #008b8b; font-weight: 600; }
        .rank-diamond { color: #4169e1; font-weight: 600; }
        .rank-ascendant { color: #228b22; font-weight: 600; }
        .rank-immortal { color: #dc143c; font-weight: 600; }
        .rank-radiant { color: #ff6347; font-weight: 600; }
        .rank-unrated { color: var(--text-secondary); font-weight: 600; }
        
        .match-card {
            background: var(--bg-secondary);
            border: 1px solid var(--border-color);
            border-radius: 8px;
            margin-bottom: 15px;
            transition: transform 0.3s ease, box-shadow 0.3s ease;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        .match-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(0, 132, 255, 0.2);
            border-color: var(--valorant-blue);
        }

        .team-red {
            border-left: 4px solid var(--valorant-red);
        }

        .team-blue {
            border-left: 4px solid var(--valorant-blue);
        }

        .win {
            background: rgba(40, 167, 69, 0.1);
            border-color: rgba(40, 167, 69, 0.3);
        }

        .loss {
            background: rgba(220, 53, 69, 0.1);
            border-color: rgba(220, 53, 69, 0.3);
        }

        .footer {
            background: var(--bg-secondary);
            border-top: 1px solid var(--border-color);
            margin-top: auto;
            color: var(--text-secondary);
        }
        
        .loading {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid var(--border-color);
            border-radius: 50%;
            border-top-color: var(--valorant-red);
            animation: spin 1s ease-in-out infinite;
        }

        @keyframes spin {
            to { transform: rotate(360deg); }
        }

        .hero-section {
            background: linear-gradient(135deg, rgba(255, 70, 85, 0.05) 0%, rgba(0, 132, 255, 0.05) 100%);
            border: 1px solid var(--border-color);
            border-radius: 15px;
            padding: 3rem 2rem;
            margin-bottom: 2rem;
            text-align: center;
        }
        
        .hero-title {
            font-size: 3rem;
            font-weight: bold;
            background: linear-gradient(135deg, var(--valorant-red) 0%, var(--valorant-blue) 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            margin-bottom: 1rem;
        }
        
        @media (max-width: 768px) {
            .hero-title {
                font-size: 2rem;
            }
        }
    </style>
    
    {% block extra_css %}{% endblock %}
</head>
<body class="d-flex flex-column">
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark">
        <div class="container">
            <a class="navbar-brand" href="{{ url_for('index') }}">
                <i class="bi bi-crosshair"></i> Valorant Rank Checker
            </a>
            
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav ms-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('index') }}">
                            <i class="bi bi-house"></i> Home
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('live_match') }}">
                            <i class="bi bi-broadcast"></i> Live Match
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('batch_lookup') }}">
                            <i class="bi bi-people"></i> Batch Lookup
                        </a>
                    </li>
                </ul>
            </div>
        </div>
    </nav>
    
    <!-- Main Content -->
    <main class="container my-4 flex-grow-1">
        {% block content %}{% endblock %}
    </main>
    
    <!-- Footer -->
    <footer class="footer py-3 mt-5">
        <div class="container text-center">
            <small class="text-muted">
                Valorant Rank Checker | Powered by 
                <a href="https://docs.henrikdev.xyz" target="_blank" class="text-decoration-none" style="color: var(--valorant-blue);">HenrikDev API</a>
                | Not affiliated with Riot Games
            </small>
        </div>
    </footer>
    
    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    {% block extra_js %}{% endblock %}
</body>
</html>
