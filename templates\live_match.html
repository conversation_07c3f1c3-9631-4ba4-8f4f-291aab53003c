{% extends "base.html" %}

{% block title %}Live Match Checker - Valorant Rank Checker{% endblock %}

{% block content %}
<div class="container">
    <!-- Hero Section -->
    <div class="hero-section">
        <h1 class="hero-title">
            <i class="bi bi-broadcast"></i> Live Match Checker
        </h1>
        <p class="lead mb-0">Check the ranks and stats of all players in your recent match</p>
        <small class="text-muted">Enter your Riot ID to see players from your most recent game</small>
    </div>

    <!-- Search Form -->
    <div class="row justify-content-center mb-4">
        <div class="col-md-8">
            <div class="card">
                <div class="card-body">
                    <form method="POST" action="{{ url_for('live_match_post') }}">
                        <div class="mb-3">
                            <label for="riot_id" class="form-label">Your Riot ID</label>
                            <input type="text" 
                                   class="form-control" 
                                   id="riot_id" 
                                   name="riot_id" 
                                   placeholder="YourName#TAG" 
                                   value="{{ riot_id or '' }}"
                                   required>
                            <div class="form-text">
                                <i class="bi bi-info-circle"></i> 
                                This will show players from your most recent match (within the last 2 hours)
                            </div>
                        </div>
                        <button type="submit" class="btn btn-primary w-100">
                            <i class="bi bi-search"></i> Find My Recent Match
                        </button>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- Error Display -->
    {% if error %}
    <div class="row justify-content-center mb-4">
        <div class="col-md-8">
            <div class="alert alert-danger" role="alert">
                <i class="bi bi-exclamation-triangle"></i> {{ error }}
            </div>
        </div>
    </div>
    {% endif %}

    <!-- Match Results -->
    {% if players %}
    <div class="row">
        <div class="col-12">
            <!-- Match Info -->
            <div class="card mb-4">
                <div class="card-header">
                    <h4 class="card-title mb-0">
                        <i class="bi bi-controller"></i> Match Information
                    </h4>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-4">
                            <strong>Map:</strong> {{ match_info.map }}
                        </div>
                        <div class="col-md-4">
                            <strong>Mode:</strong> {{ match_info.mode }}
                        </div>
                        <div class="col-md-4">
                            <strong>Started:</strong> {{ match_info.started }}
                        </div>
                    </div>
                </div>
            </div>

            <!-- Players by Team -->
            {% set red_team = players | selectattr('team', 'equalto', 'Red') | list %}
            {% set blue_team = players | selectattr('team', 'equalto', 'Blue') | list %}
            
            <div class="row">
                <!-- Red Team -->
                <div class="col-lg-6 mb-4">
                    <div class="card team-red">
                        <div class="card-header bg-danger text-white">
                            <h5 class="card-title mb-0">
                                <i class="bi bi-circle-fill text-danger"></i> Red Team
                            </h5>
                        </div>
                        <div class="card-body">
                            {% for player in red_team %}
                            <div class="player-card mb-3 p-3 border rounded">
                                <div class="row align-items-center">
                                    <div class="col-md-6">
                                        <h6 class="mb-1">{{ player.name }}#{{ player.tag }}</h6>
                                        <span class="badge bg-secondary">{{ player.agent }}</span>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="rank-info">
                                            {% set rank_class = 'rank-' + player.rank.lower().replace(' ', '-') %}
                                            <span class="rank {{ rank_class }}">{{ player.rank }}</span>
                                            {% if player.rr > 0 %}
                                            <small class="text-muted d-block">{{ player.rr }} RR</small>
                                            {% endif %}
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="stats">
                                            <small class="text-muted">
                                                {{ player.stats.kills }}/{{ player.stats.deaths }}/{{ player.stats.assists }}
                                            </small>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            {% endfor %}
                        </div>
                    </div>
                </div>

                <!-- Blue Team -->
                <div class="col-lg-6 mb-4">
                    <div class="card team-blue">
                        <div class="card-header bg-primary text-white">
                            <h5 class="card-title mb-0">
                                <i class="bi bi-circle-fill text-primary"></i> Blue Team
                            </h5>
                        </div>
                        <div class="card-body">
                            {% for player in blue_team %}
                            <div class="player-card mb-3 p-3 border rounded">
                                <div class="row align-items-center">
                                    <div class="col-md-6">
                                        <h6 class="mb-1">{{ player.name }}#{{ player.tag }}</h6>
                                        <span class="badge bg-secondary">{{ player.agent }}</span>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="rank-info">
                                            {% set rank_class = 'rank-' + player.rank.lower().replace(' ', '-') %}
                                            <span class="rank {{ rank_class }}">{{ player.rank }}</span>
                                            {% if player.rr > 0 %}
                                            <small class="text-muted d-block">{{ player.rr }} RR</small>
                                            {% endif %}
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="stats">
                                            <small class="text-muted">
                                                {{ player.stats.kills }}/{{ player.stats.deaths }}/{{ player.stats.assists }}
                                            </small>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            {% endfor %}
                        </div>
                    </div>
                </div>
            </div>

            <!-- Summary Stats -->
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="bi bi-bar-chart"></i> Quick Summary
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row text-center">
                        <div class="col-md-3">
                            <h6>Total Players</h6>
                            <span class="badge bg-info fs-6">{{ players | length }}</span>
                        </div>
                        <div class="col-md-3">
                            <h6>Red Team</h6>
                            <span class="badge bg-danger fs-6">{{ red_team | length }}</span>
                        </div>
                        <div class="col-md-3">
                            <h6>Blue Team</h6>
                            <span class="badge bg-primary fs-6">{{ blue_team | length }}</span>
                        </div>
                        <div class="col-md-3">
                            <h6>Map</h6>
                            <span class="badge bg-secondary fs-6">{{ match_info.map }}</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    {% endif %}

    <!-- Info Section -->
    {% if not players and not error %}
    <div class="row justify-content-center">
        <div class="col-md-8">
            <div class="alert alert-info" role="alert">
                <h5 class="alert-heading">
                    <i class="bi bi-info-circle"></i> How it works
                </h5>
                <p>This tool checks your most recent Valorant match and shows the ranks and basic stats of all players in that game.</p>
                <hr>
                <ul class="mb-0">
                    <li>Enter your Riot ID (Name#TAG)</li>
                    <li>We'll find your most recent match (within the last 2 hours)</li>
                    <li>Display all 10 players with their ranks, agents, and KDA</li>
                    <li>Players are organized by team (Red vs Blue)</li>
                </ul>
            </div>
        </div>
    </div>
    {% endif %}
</div>
{% endblock %}

{% block extra_css %}
<style>
    .player-card {
        transition: all 0.3s ease;
        background: var(--bg-secondary);
    }
    
    .player-card:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 8px rgba(0,0,0,0.1);
    }
    
    .rank {
        font-weight: 600;
        font-size: 0.9rem;
    }
    
    .team-red .card-header {
        background: linear-gradient(135deg, #dc3545 0%, #c82333 100%) !important;
    }
    
    .team-blue .card-header {
        background: linear-gradient(135deg, #007bff 0%, #0056b3 100%) !important;
    }
    
    .stats {
        font-family: 'Courier New', monospace;
        font-weight: 600;
    }
</style>
{% endblock %}
