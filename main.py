#!/usr/bin/env python3
"""
Valorant Rank Checker Tool

A Python application to check ranks and player information using the HenrikDev API.
"""

import sys
import os
from typing import List

# Add src directory to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from rich.console import Console
from rich.panel import Panel
from rich.text import Text
from rich.prompt import Prompt, Confirm
from rich import box

from src.api_client import ValorantAPI, APIError, RateLimitError
from src.models import Player
from src.utils import (
    parse_riot_id, validate_region, validate_platform,
    print_error, print_success, print_info, print_warning,
    create_player_table, console
)
from src.config import config


class ValorantTool:
    """Main application class for the Valorant Tool."""
    
    def __init__(self):
        """Initialize the Valorant Tool."""
        self.api = ValorantAPI()
        self.console = console
    
    def display_banner(self):
        """Display the application banner."""
        banner = Text("VALORANT RANK CHECKER", style="bold cyan")
        subtitle = Text("Check player ranks and match information", style="dim")
        
        panel = Panel.fit(
            f"{banner}\n{subtitle}",
            box=box.DOUBLE,
            border_style="cyan"
        )
        self.console.print(panel)
        self.console.print()
    
    def display_menu(self):
        """Display the main menu."""
        menu_text = """
[bold cyan]Main Menu:[/bold cyan]

[bold]1.[/bold] Quick Player Lookup
[bold]2.[/bold] Batch Player Lookup
[bold]3.[/bold] Settings
[bold]4.[/bold] Exit

Choose an option (1-4):"""
        
        self.console.print(Panel(menu_text, title="Menu", border_style="blue"))
    
    def quick_player_lookup(self):
        """Handle single player lookup."""
        self.console.print("\n[bold cyan]Quick Player Lookup[/bold cyan]")
        self.console.print("Enter Riot ID in format: name#tag")
        
        riot_id = Prompt.ask("Riot ID")
        
        try:
            name, tag = parse_riot_id(riot_id)
            
            with self.console.status(f"[bold green]Looking up {riot_id}..."):
                player = self.api.get_player_full_info(name, tag)
            
            if player:
                self.display_player_info(player)
            else:
                print_error(f"Player {riot_id} not found.")
                
        except ValueError as e:
            print_error(str(e))
        except RateLimitError as e:
            print_error(str(e))
            print_info("Please wait a moment before making another request.")
        except APIError as e:
            print_error(str(e))
    
    def batch_player_lookup(self):
        """Handle multiple player lookup."""
        self.console.print("\n[bold cyan]Batch Player Lookup[/bold cyan]")
        self.console.print("Enter multiple Riot IDs separated by commas or spaces")
        self.console.print("Example: player1#tag1, player2#tag2, player3#tag3")
        
        input_text = Prompt.ask("Riot IDs")
        
        # Parse multiple Riot IDs
        riot_ids = []
        for item in input_text.replace(',', ' ').split():
            item = item.strip()
            if item:
                riot_ids.append(item)
        
        if not riot_ids:
            print_error("No valid Riot IDs provided.")
            return
        
        players = []
        failed_lookups = []
        
        for riot_id in riot_ids:
            try:
                name, tag = parse_riot_id(riot_id)
                
                with self.console.status(f"[bold green]Looking up {riot_id}..."):
                    player = self.api.get_player_full_info(name, tag)
                
                if player:
                    players.append(player)
                else:
                    failed_lookups.append(riot_id)
                    
            except ValueError as e:
                print_error(f"Invalid format for {riot_id}: {str(e)}")
                failed_lookups.append(riot_id)
            except (RateLimitError, APIError) as e:
                print_error(f"Failed to lookup {riot_id}: {str(e)}")
                failed_lookups.append(riot_id)
        
        # Display results
        if players:
            table = create_player_table(players, "Player Lookup Results")
            self.console.print(table)
        
        if failed_lookups:
            print_warning(f"Failed to lookup: {', '.join(failed_lookups)}")
    
    def display_player_info(self, player: Player):
        """Display detailed information for a single player."""
        info_text = f"""
[bold cyan]Player Information[/bold cyan]

[bold]Riot ID:[/bold] {player.riot_id}
[bold]Account Level:[/bold] {player.account_level}
[bold]Platform:[/bold] {player.platform.upper()}
[bold]Region:[/bold] {player.region.upper()}

[bold]Current Rank:[/bold] {player.display_rank}
[bold]Peak Rank:[/bold] {player.display_peak}
"""
        
        if player.current_rank and player.current_rank.leaderboard_rank:
            info_text += f"[bold]Leaderboard Rank:[/bold] #{player.current_rank.leaderboard_rank}\n"
        
        if player.updated_at:
            info_text += f"[bold]Last Updated:[/bold] {player.updated_at.strftime('%Y-%m-%d %H:%M:%S UTC')}\n"
        
        panel = Panel(info_text, title=f"Player: {player.riot_id}", border_style="green")
        self.console.print(panel)
    
    def settings_menu(self):
        """Display and handle settings."""
        self.console.print("\n[bold cyan]Settings[/bold cyan]")
        
        settings_text = f"""
[bold]Current Settings:[/bold]

[bold]Default Region:[/bold] {config.default_region.upper()}
[bold]Default Platform:[/bold] {config.default_platform.upper()}
[bold]API Key:[/bold] {'Set' if config.api_key else 'Not Set'}

[dim]Note: To change settings, edit your .env file[/dim]
"""
        
        panel = Panel(settings_text, title="Configuration", border_style="yellow")
        self.console.print(panel)
        
        Prompt.ask("Press Enter to continue")
    
    def run(self):
        """Run the main application loop."""
        try:
            self.display_banner()
            
            while True:
                self.display_menu()
                
                choice = Prompt.ask("Choice", choices=["1", "2", "3", "4"], default="1")
                
                if choice == "1":
                    self.quick_player_lookup()
                elif choice == "2":
                    self.batch_player_lookup()
                elif choice == "3":
                    self.settings_menu()
                elif choice == "4":
                    print_success("Thanks for using Valorant Rank Checker!")
                    break
                
                self.console.print()
                
        except KeyboardInterrupt:
            self.console.print("\n[bold red]Interrupted by user[/bold red]")
        except Exception as e:
            print_error(f"Unexpected error: {str(e)}")
            self.console.print("[dim]Please report this issue if it persists.[/dim]")


def main():
    """Main entry point."""
    try:
        app = ValorantTool()
        app.run()
    except ValueError as e:
        print_error(str(e))
        print_info("Please check your .env file and API key configuration.")
        return 1
    except Exception as e:
        print_error(f"Failed to start application: {str(e)}")
        return 1
    
    return 0


if __name__ == "__main__":
    sys.exit(main())
