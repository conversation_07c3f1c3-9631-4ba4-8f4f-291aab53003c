"""Configuration management for Valorant Tool."""

import os
from pathlib import Path
from dotenv import load_dotenv


class Config:
    """Configuration manager for the Valorant tool."""
    
    def __init__(self):
        """Initialize configuration by loading environment variables."""
        # Load .env file if it exists
        env_path = Path('.env')
        if env_path.exists():
            load_dotenv(env_path)
        
        # API Configuration
        self.api_key = os.getenv('HENRIKDEV_API_KEY')
        self.base_url = 'https://api.henrikdev.xyz'
        
        # Default settings
        self.default_region = os.getenv('DEFAULT_REGION', 'na').lower()
        self.default_platform = os.getenv('DEFAULT_PLATFORM', 'pc').lower()
        
        # Validate configuration
        self._validate()
    
    def _validate(self):
        """Validate configuration settings."""
        if not self.api_key:
            raise ValueError(
                "API key not found. Please set HENRIKDEV_API_KEY in your .env file.\n"
                "Get your API key from: https://discord.com/invite/X3GaVkX2YN"
            )
        
        valid_regions = ['eu', 'na', 'ap', 'kr', 'latam', 'br']
        if self.default_region not in valid_regions:
            raise ValueError(f"Invalid region: {self.default_region}. Valid regions: {valid_regions}")
        
        valid_platforms = ['pc', 'console']
        if self.default_platform not in valid_platforms:
            raise ValueError(f"Invalid platform: {self.default_platform}. Valid platforms: {valid_platforms}")
    
    @property
    def headers(self):
        """Get HTTP headers for API requests."""
        return {
            'Authorization': self.api_key,
            'Accept': 'application/json',
            'User-Agent': 'ValorantTool/1.0'
        }


# Global config instance
config = Config()
