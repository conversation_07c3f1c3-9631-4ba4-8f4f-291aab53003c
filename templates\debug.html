{% extends "base.html" %}

{% block title %}Valorant Client Debug{% endblock %}

{% block content %}
<div class="container mt-4">
    <div class="row">
        <div class="col-12">
            <h1 class="mb-4">
                <i class="fas fa-bug"></i>
                Valorant Client Debug Information
            </h1>
            
            {% if debug_info.debug_failed %}
                <div class="alert alert-danger">
                    <h4><i class="fas fa-exclamation-triangle"></i> Debug Failed</h4>
                    <p>{{ debug_info.error }}</p>
                </div>
            {% else %}
                <!-- Status Overview -->
                <div class="row mb-4">
                    <div class="col-md-3">
                        <div class="card {% if debug_info.valorant_running %}border-success{% else %}border-danger{% endif %}">
                            <div class="card-body text-center">
                                <i class="fas fa-gamepad fa-2x {% if debug_info.valorant_running %}text-success{% else %}text-danger{% endif %}"></i>
                                <h5 class="card-title mt-2">Valorant Running</h5>
                                <p class="card-text">
                                    {% if debug_info.valorant_running %}
                                        <span class="badge badge-success">Yes</span>
                                    {% else %}
                                        <span class="badge badge-danger">No</span>
                                    {% endif %}
                                </p>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-md-3">
                        <div class="card {% if debug_info.process_found %}border-success{% else %}border-danger{% endif %}">
                            <div class="card-body text-center">
                                <i class="fas fa-search fa-2x {% if debug_info.process_found %}text-success{% else %}text-danger{% endif %}"></i>
                                <h5 class="card-title mt-2">Process Found</h5>
                                <p class="card-text">
                                    {% if debug_info.process_found %}
                                        <span class="badge badge-success">Yes</span>
                                    {% else %}
                                        <span class="badge badge-danger">No</span>
                                    {% endif %}
                                </p>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-md-3">
                        <div class="card {% if debug_info.connection_details_found %}border-success{% else %}border-warning{% endif %}">
                            <div class="card-body text-center">
                                <i class="fas fa-key fa-2x {% if debug_info.connection_details_found %}text-success{% else %}text-warning{% endif %}"></i>
                                <h5 class="card-title mt-2">Connection Details</h5>
                                <p class="card-text">
                                    {% if debug_info.connection_details_found %}
                                        <span class="badge badge-success">Found</span>
                                    {% else %}
                                        <span class="badge badge-warning">Missing</span>
                                    {% endif %}
                                </p>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-md-3">
                        <div class="card {% if debug_info.connection_successful %}border-success{% else %}border-danger{% endif %}">
                            <div class="card-body text-center">
                                <i class="fas fa-wifi fa-2x {% if debug_info.connection_successful %}text-success{% else %}text-danger{% endif %}"></i>
                                <h5 class="card-title mt-2">Connection</h5>
                                <p class="card-text">
                                    {% if debug_info.connection_successful %}
                                        <span class="badge badge-success">Success</span>
                                    {% else %}
                                        <span class="badge badge-danger">Failed</span>
                                    {% endif %}
                                </p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Process Information -->
                {% if debug_info.processes %}
                <div class="card mb-4">
                    <div class="card-header">
                        <h5><i class="fas fa-list"></i> Valorant Processes ({{ debug_info.processes|length }})</h5>
                    </div>
                    <div class="card-body">
                        {% for process in debug_info.processes %}
                        <div class="mb-3 p-3 border rounded">
                            <h6>Process ID: {{ process.pid }}</h6>
                            <p><strong>Command Line Length:</strong> {{ process.cmdline_length }} characters</p>
                            <p><strong>Command Line Preview:</strong></p>
                            <code class="d-block p-2 bg-light">{{ process.cmdline_preview }}</code>
                        </div>
                        {% endfor %}
                    </div>
                </div>
                {% endif %}

                <!-- Connection Details -->
                {% if debug_info.connection_details %}
                <div class="card mb-4">
                    <div class="card-header">
                        <h5><i class="fas fa-network-wired"></i> Connection Details</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <p><strong>Port:</strong> {{ debug_info.connection_details.port }}</p>
                                <p><strong>Process ID:</strong> {{ debug_info.connection_details.pid }}</p>
                            </div>
                            <div class="col-md-6">
                                <p><strong>Password Length:</strong> {{ debug_info.connection_details.password_length }} characters</p>
                                <p><strong>Password Preview:</strong> <code>{{ debug_info.connection_details.password_preview }}</code></p>
                            </div>
                        </div>
                    </div>
                </div>
                {% endif %}

                <!-- Test Results -->
                {% if debug_info.test_results %}
                <div class="card mb-4">
                    <div class="card-header">
                        <h5><i class="fas fa-flask"></i> Endpoint Test Results</h5>
                    </div>
                    <div class="card-body">
                        {% for endpoint, result in debug_info.test_results.items() %}
                        <div class="mb-3 p-3 border rounded {% if result.success %}border-success{% else %}border-danger{% endif %}">
                            <h6>{{ endpoint }}</h6>
                            {% if result.success %}
                                <span class="badge badge-success">Success</span>
                                <p class="mt-2"><strong>Status Code:</strong> {{ result.status_code }}</p>
                                {% if result.response_preview %}
                                <p><strong>Response Preview:</strong></p>
                                <code class="d-block p-2 bg-light">{{ result.response_preview }}</code>
                                {% endif %}
                            {% else %}
                                <span class="badge badge-danger">Failed</span>
                                {% if result.status_code %}
                                <p class="mt-2"><strong>Status Code:</strong> {{ result.status_code }}</p>
                                {% endif %}
                                {% if result.error %}
                                <p class="mt-2"><strong>Error:</strong> {{ result.error }}</p>
                                {% endif %}
                            {% endif %}
                        </div>
                        {% endfor %}
                    </div>
                </div>
                {% endif %}

                <!-- Manual Extraction Results -->
                {% if debug_info.manual_extraction %}
                <div class="card mb-4">
                    <div class="card-header">
                        <h5><i class="fas fa-search"></i> Manual Extraction Attempts</h5>
                    </div>
                    <div class="card-body">
                        <p><strong>Potential Ports Found:</strong> {{ debug_info.manual_extraction.potential_ports|join(', ') or 'None' }}</p>
                        <p><strong>Potential Auth Tokens Found:</strong> {{ debug_info.manual_extraction.potential_auth_count }}</p>
                    </div>
                </div>
                {% endif %}

                <!-- Errors -->
                {% if debug_info.errors %}
                <div class="card mb-4">
                    <div class="card-header bg-danger text-white">
                        <h5><i class="fas fa-exclamation-triangle"></i> Errors</h5>
                    </div>
                    <div class="card-body">
                        {% for error in debug_info.errors %}
                        <div class="alert alert-danger">{{ error }}</div>
                        {% endfor %}
                    </div>
                </div>
                {% endif %}

                <!-- Troubleshooting Tips -->
                <div class="card mb-4">
                    <div class="card-header bg-info text-white">
                        <h5><i class="fas fa-lightbulb"></i> Troubleshooting Tips</h5>
                    </div>
                    <div class="card-body">
                        <ul>
                            <li><strong>If Valorant is not running:</strong> Start Valorant and make sure you're logged in.</li>
                            <li><strong>If process is found but connection details are missing:</strong> Try restarting Valorant or check if it's running with administrator privileges.</li>
                            <li><strong>If connection fails:</strong> Make sure Valorant is fully loaded (not just the launcher) and try refreshing this page.</li>
                            <li><strong>If authentication fails:</strong> The auth token might have changed - try restarting Valorant.</li>
                            <li><strong>Firewall issues:</strong> Make sure your firewall isn't blocking local connections to Valorant.</li>
                        </ul>
                    </div>
                </div>
            {% endif %}

            <!-- Actions -->
            <div class="text-center">
                <a href="/live-match" class="btn btn-primary">
                    <i class="fas fa-arrow-left"></i> Back to Live Match
                </a>
                <button onclick="location.reload()" class="btn btn-secondary">
                    <i class="fas fa-sync"></i> Refresh Debug Info
                </button>
            </div>
        </div>
    </div>
</div>
{% endblock %}
