{% extends "base.html" %}

{% block title %}Valorant Rank Checker - Home{% endblock %}

{% block content %}
<div class="hero-section">
    <h1 class="hero-title">
        <i class="bi bi-crosshair"></i> Valorant Rank Checker
    </h1>
    <p class="lead mb-4">
        Check player ranks, match history, and analyze your teammates instantly
    </p>
    <p class="text-muted">
        Enter any Riot ID to see current rank, peak rank, and recent match performance
    </p>
</div>

<div class="row justify-content-center">
    <div class="col-lg-8">
        <div class="card">
            <div class="card-header">
                <h3 class="card-title mb-0">
                    <i class="bi bi-search"></i> Player Lookup
                </h3>
            </div>
            <div class="card-body">
                <form action="{{ url_for('search_player') }}" method="POST">
                    <div class="mb-3">
                        <label for="riot_id" class="form-label">Riot ID</label>
                        <input 
                            type="text" 
                            class="form-control form-control-lg" 
                            id="riot_id" 
                            name="riot_id" 
                            placeholder="Enter Riot ID (e.g., TenZ#SEN)" 
                            required
                            pattern="[^#]+#[^#]+"
                            title="Please enter a valid Riot ID in the format: name#tag"
                        >
                        <div class="form-text text-muted">
                            Format: name#tag (e.g., TenZ#SEN, Shroud#C9)
                        </div>
                    </div>
                    
                    <div class="d-grid">
                        <button type="submit" class="btn btn-primary btn-lg">
                            <i class="bi bi-search"></i> Look Up Player
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<div class="row mt-5">
    <div class="col-lg-4 mb-4">
        <div class="card h-100">
            <div class="card-body text-center">
                <i class="bi bi-person-badge display-4 text-primary mb-3"></i>
                <h5 class="card-title">Player Profiles</h5>
                <p class="card-text">
                    View detailed player information including current rank, peak rank, account level, and platform.
                </p>
            </div>
        </div>
    </div>
    
    <div class="col-lg-4 mb-4">
        <div class="card h-100">
            <div class="card-body text-center">
                <i class="bi bi-clock-history display-4 text-info mb-3"></i>
                <h5 class="card-title">Match History</h5>
                <p class="card-text">
                    See recent matches with performance stats, KDA, damage dealt, and match outcomes.
                </p>
            </div>
        </div>
    </div>
    
    <div class="col-lg-4 mb-4">
        <div class="card h-100">
            <div class="card-body text-center">
                <i class="bi bi-people display-4 text-success mb-3"></i>
                <h5 class="card-title">Match Analysis</h5>
                <p class="card-text">
                    Click on any match to see all 10 players, their ranks, and detailed match statistics.
                </p>
            </div>
        </div>
    </div>
</div>

<div class="row mt-4">
    <div class="col-lg-6 mb-4">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="bi bi-people-fill"></i> Batch Lookup
                </h5>
            </div>
            <div class="card-body">
                <p class="card-text">
                    Need to check multiple players at once? Use our batch lookup feature to analyze entire teams.
                </p>
                <a href="{{ url_for('batch_lookup') }}" class="btn btn-outline-primary">
                    <i class="bi bi-arrow-right"></i> Batch Lookup
                </a>
            </div>
        </div>
    </div>
    
    <div class="col-lg-6 mb-4">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="bi bi-info-circle"></i> How It Works
                </h5>
            </div>
            <div class="card-body">
                <ol class="mb-0">
                    <li>Enter a Riot ID (name#tag)</li>
                    <li>View player rank and stats</li>
                    <li>Browse match history</li>
                    <li>Click matches to see all players</li>
                </ol>
            </div>
        </div>
    </div>
</div>

<div class="alert alert-info mt-4" role="alert">
    <i class="bi bi-lightbulb"></i>
    <strong>Pro Tip:</strong> 
    You can bookmark player profile pages and share them with friends. 
    All data is fetched in real-time from the official Valorant API.
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    const form = document.querySelector('form');
    const submitBtn = document.querySelector('button[type="submit"]');
    const originalText = submitBtn.innerHTML;
    
    form.addEventListener('submit', function() {
        submitBtn.innerHTML = '<span class="loading"></span> Looking up...';
        submitBtn.disabled = true;
    });
    
    // Reset button if user navigates back
    window.addEventListener('pageshow', function() {
        submitBtn.innerHTML = originalText;
        submitBtn.disabled = false;
    });
});
</script>
{% endblock %}
