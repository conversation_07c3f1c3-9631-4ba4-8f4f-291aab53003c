{% extends "base.html" %}

{% block title %}Batch Lookup Results - Valorant Rank Checker{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h2>
        <i class="bi bi-people-fill"></i> Batch Lookup Results
    </h2>
    <a href="{{ url_for('batch_lookup') }}" class="btn btn-outline-primary">
        <i class="bi bi-plus"></i> New Batch Lookup
    </a>
</div>

{% if players %}
<div class="card mb-4">
    <div class="card-header">
        <h4 class="card-title mb-0">
            <i class="bi bi-check-circle text-success"></i> 
            Found {{ players|length }} Player{{ 's' if players|length != 1 else '' }}
        </h4>
    </div>
    <div class="card-body">
        <div class="row">
            {% for player in players %}
            <div class="col-lg-6 col-xl-4 mb-3">
                <div class="card h-100">
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-start mb-2">
                            <h6 class="card-title mb-0">{{ player.riot_id }}</h6>
                            <span class="badge bg-secondary">{{ player.account_level }}</span>
                        </div>
                        
                        <div class="mb-2">
                            <strong>Current Rank:</strong>
                            {% if player.current_rank %}
                                {% set rank_class = 'rank-' + player.current_rank.current_tier_name.lower().split()[0] %}
                                <span class="{{ rank_class }}">{{ player.current_rank.current_tier_name }}</span>
                                {% if player.current_rank.rr > 0 %}
                                    <small class="text-muted">({{ player.current_rank.rr }} RR)</small>
                                {% endif %}
                            {% else %}
                                <span class="text-muted">No Competitive Data</span>
                            {% endif %}
                        </div>
                        
                        <div class="mb-2">
                            <strong>Peak Rank:</strong>
                            {% if player.peak_rank %}
                                {% set peak_rank_class = 'rank-' + player.peak_rank.tier_name.lower().split()[0] %}
                                <span class="{{ peak_rank_class }}">{{ player.peak_rank.tier_name }}</span>
                                <small class="text-muted">({{ player.peak_rank.season }})</small>
                            {% else %}
                                <span class="text-muted">No Competitive Data</span>
                            {% endif %}
                        </div>
                        
                        <div class="row">
                            <div class="col-6">
                                <small class="text-muted">Platform:</small><br>
                                <span class="badge bg-info">{{ player.platform.upper() }}</span>
                            </div>
                            <div class="col-6">
                                <small class="text-muted">Region:</small><br>
                                <span class="badge bg-primary">{{ player.region.upper() }}</span>
                            </div>
                        </div>
                        
                        <div class="mt-3">
                            <a href="{{ url_for('player_profile', name=player.name, tag=player.tag) }}" 
                               class="btn btn-outline-primary btn-sm w-100">
                                <i class="bi bi-eye"></i> View Full Profile
                            </a>
                        </div>
                    </div>
                </div>
            </div>
            {% endfor %}
        </div>
    </div>
</div>
{% endif %}

{% if failed_lookups %}
<div class="card">
    <div class="card-header">
        <h4 class="card-title mb-0">
            <i class="bi bi-exclamation-triangle text-warning"></i> 
            Failed Lookups ({{ failed_lookups|length }})
        </h4>
    </div>
    <div class="card-body">
        <p class="text-muted mb-3">
            The following Riot IDs could not be found or had errors:
        </p>
        <div class="row">
            {% for riot_id in failed_lookups %}
            <div class="col-md-6 col-lg-4 mb-2">
                <span class="badge bg-danger">{{ riot_id }}</span>
            </div>
            {% endfor %}
        </div>
        
        <div class="alert alert-info mt-3" role="alert">
            <i class="bi bi-info-circle"></i>
            <strong>Common reasons for failed lookups:</strong>
            <ul class="mb-0 mt-2">
                <li>Invalid Riot ID format (should be name#tag)</li>
                <li>Player not found or hasn't played recently</li>
                <li>Temporary API issues or rate limits</li>
                <li>Player from a different region</li>
            </ul>
        </div>
    </div>
</div>
{% endif %}

{% if not players and not failed_lookups %}
<div class="card">
    <div class="card-body text-center py-5">
        <i class="bi bi-search display-4 text-muted mb-3"></i>
        <h4 class="text-muted">No Results</h4>
        <p class="text-muted">No valid Riot IDs were found in your input.</p>
        <a href="{{ url_for('batch_lookup') }}" class="btn btn-primary">
            <i class="bi bi-arrow-left"></i> Try Again
        </a>
    </div>
</div>
{% endif %}

<div class="text-center mt-4">
    <a href="{{ url_for('index') }}" class="btn btn-outline-secondary">
        <i class="bi bi-house"></i> Back to Home
    </a>
</div>
{% endblock %}
