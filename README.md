# 🎯 Valorant Rank Checker Tool

A Python application to check player ranks and information using the HenrikDev API. Perfect for checking teammate ranks in lobby or analyzing match data.

## ✨ Features

- **Quick Player Lookup** - Check individual player ranks by Riot ID
- **Batch Player Lookup** - Check multiple players at once
- **Detailed Player Info** - Current rank, peak rank, account level, platform
- **Beautiful CLI Interface** - Rich formatting with colors and tables
- **Rate Limit Handling** - Automatic rate limiting to prevent API errors
- **Error Handling** - Graceful handling of network issues and invalid data

## 📋 Requirements

- Python 3.7+
- HenrikDev API key (free)
- Internet connection

## 🚀 Quick Start

### 1. Get API Key

1. Join the HenrikDev Discord: https://discord.com/invite/X3GaVkX2YN
2. Verify your account
3. Go to the `#get-a-key` channel
4. Select "VALORANT" from the dropdown
5. Choose "Basic" (30 req/min) or "Advanced" (90 req/min, requires approval)

### 2. Installation

```bash
# Clone or download this repository
cd valoTool

# Install dependencies
pip install -r requirements.txt

# Copy environment file
cp .env.example .env

# Edit .env file and add your API key
# HENRIKDEV_API_KEY=your_api_key_here
```

### 3. Run the Application

```bash
python main.py
```

## 🎮 Usage

### Quick Player Lookup
Enter a single Riot ID to get detailed player information:
```
Riot ID: TenZ#SEN
```

### Batch Player Lookup
Enter multiple Riot IDs separated by commas or spaces:
```
Riot IDs: TenZ#SEN, Shroud#C9, s0m#NRG
```

## 📊 Information Displayed

For each player, you'll see:
- **Riot ID** (name#tag)
- **Current Rank** with RR (Rank Rating)
- **Peak Rank** and season achieved
- **Account Level**
- **Platform** (PC/Console)
- **Leaderboard Rank** (for high-ranked players)

## ⚙️ Configuration

Edit your `.env` file to customize settings:

```env
# Your HenrikDev API key
HENRIKDEV_API_KEY=your_api_key_here

# Default region (eu, na, ap, kr, latam, br)
DEFAULT_REGION=na

# Default platform (pc, console)
DEFAULT_PLATFORM=pc
```

## 🌍 Supported Regions

- **EU** - Europe
- **NA** - North America
- **AP** - Asia Pacific
- **KR** - Korea
- **LATAM** - Latin America
- **BR** - Brazil

## 🔧 Troubleshooting

### Common Issues

**"API key not found"**
- Make sure you've created a `.env` file
- Check that your API key is correctly set in the `.env` file
- Ensure there are no extra spaces around the API key

**"Rate limit exceeded"**
- Wait a moment before making more requests
- Basic tier allows 30 requests per minute
- Consider upgrading to Advanced tier for 90 requests per minute

**"Player not found"**
- Double-check the Riot ID format (name#tag)
- Make sure the player exists and has played Valorant recently
- Try a different region if the player might be from another region

**"Network error"**
- Check your internet connection
- The Riot API or HenrikDev API might be temporarily down
- Try again in a few minutes

### Getting Help

If you encounter issues:
1. Check the error message for specific details
2. Verify your API key and configuration
3. Make sure you're using the correct Riot ID format
4. Check the HenrikDev Discord for API status updates

## 📝 API Rate Limits

- **Basic Tier**: 30 requests per minute
- **Advanced Tier**: 90 requests per minute (requires approval)

The tool automatically handles rate limiting to prevent errors.

## 🤝 Contributing

Feel free to submit issues, feature requests, or pull requests to improve the tool.

## 📄 License

This project is for educational purposes. Respect the HenrikDev API terms of service.

## 🙏 Credits

- **HenrikDev API** - For providing the Valorant data API
- **Riot Games** - For Valorant
- **Rich Library** - For beautiful CLI formatting

---

**Disclaimer**: This tool is not affiliated with Riot Games or HenrikDev. Use responsibly and respect API rate limits.
