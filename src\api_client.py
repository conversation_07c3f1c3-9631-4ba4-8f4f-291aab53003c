"""HenrikDev API client for Valorant data."""

import requests
import time
from typing import Optional, List, Dict, Any, Tuple
from datetime import datetime

from .config import config
from .models import Player, RankInfo, PeakRank, MatchInfo, MatchPlayer
from .utils import print_error, print_warning


class APIError(Exception):
    """Custom exception for API errors."""
    pass


class RateLimitError(APIError):
    """Exception for rate limit errors."""
    pass


class ValorantAPI:
    """Client for interacting with HenrikDev Valorant API."""
    
    def __init__(self):
        """Initialize the API client."""
        self.base_url = config.base_url
        self.headers = config.headers
        self.session = requests.Session()
        self.session.headers.update(self.headers)
        
        # Rate limiting
        self.last_request_time = 0
        self.min_request_interval = 2.0  # 2 seconds between requests for basic tier
    
    def _make_request(self, endpoint: str, params: Optional[Dict] = None) -> Dict[str, Any]:
        """
        Make a request to the API with rate limiting and error handling.
        
        Args:
            endpoint: API endpoint
            params: Query parameters
            
        Returns:
            JSON response data
            
        Raises:
            APIError: For API-related errors
            RateLimitError: For rate limit errors
        """
        # Rate limiting
        current_time = time.time()
        time_since_last = current_time - self.last_request_time
        if time_since_last < self.min_request_interval:
            sleep_time = self.min_request_interval - time_since_last
            time.sleep(sleep_time)
        
        url = f"{self.base_url}{endpoint}"
        
        try:
            response = self.session.get(url, params=params, timeout=10)
            self.last_request_time = time.time()
            
            # Handle different status codes
            if response.status_code == 200:
                data = response.json()
                if data.get('status') != 200:
                    raise APIError(f"API returned error status: {data.get('status')}")
                return data
            
            elif response.status_code == 429:
                raise RateLimitError("Rate limit exceeded. Please wait before making more requests.")
            
            elif response.status_code == 404:
                raise APIError("Player or data not found.")
            
            elif response.status_code == 403:
                raise APIError("API access forbidden. Check your API key or try again later.")
            
            elif response.status_code == 408:
                raise APIError("Request timeout. Riot API may be slow.")
            
            elif response.status_code == 503:
                raise APIError("Riot API is currently down.")
            
            else:
                raise APIError(f"API request failed with status {response.status_code}")
                
        except requests.RequestException as e:
            raise APIError(f"Network error: {str(e)}")
    
    def get_account_info(self, name: str, tag: str, region: str = None) -> Optional[Player]:
        """
        Get account information for a player.
        
        Args:
            name: Player name
            tag: Player tag
            region: Region (optional, uses default if not provided)
            
        Returns:
            Player object or None if not found
        """
        try:
            endpoint = f"/valorant/v2/account/{name}/{tag}"
            data = self._make_request(endpoint)
            
            account_data = data['data']
            
            # Parse updated_at timestamp
            updated_at = None
            if 'updated_at' in account_data:
                updated_at = datetime.fromisoformat(account_data['updated_at'].replace('Z', '+00:00'))
            
            return Player(
                puuid=account_data['puuid'],
                name=account_data['name'],
                tag=account_data['tag'],
                region=account_data['region'],
                account_level=account_data['account_level'],
                platform=account_data.get('platforms', ['PC'])[0].lower(),
                card_id=account_data.get('card'),
                title_id=account_data.get('title'),
                updated_at=updated_at
            )
            
        except APIError:
            raise
        except Exception as e:
            raise APIError(f"Failed to parse account data: {str(e)}")
    
    def get_mmr_info(self, name: str, tag: str, region: str = None, platform: str = None) -> Tuple[Optional[RankInfo], Optional[PeakRank]]:
        """
        Get MMR/rank information for a player.
        
        Args:
            name: Player name
            tag: Player tag
            region: Region (optional, uses default if not provided)
            platform: Platform (optional, uses default if not provided)
            
        Returns:
            Tuple of (current_rank, peak_rank) or (None, None) if not found
        """
        region = region or config.default_region
        platform = platform or config.default_platform
        
        try:
            endpoint = f"/valorant/v3/mmr/{region}/{platform}/{name}/{tag}"
            data = self._make_request(endpoint)

            mmr_data = data.get('data')
            if not mmr_data:
                return None, None

            # Parse current rank
            current_rank = None
            if 'current' in mmr_data and mmr_data['current']:
                current_data = mmr_data['current']
                if current_data and 'tier' in current_data and current_data['tier']:
                    current_rank = RankInfo(
                        current_tier_id=current_data['tier'].get('id', 0),
                        current_tier_name=current_data['tier'].get('name', 'Unrated'),
                        rr=current_data.get('rr', 0),
                        last_change=current_data.get('last_change', 0),
                        elo=current_data.get('elo', 0),
                        games_needed_for_rating=current_data.get('games_needed_for_rating', 0),
                        leaderboard_rank=current_data.get('leaderboard_placement', {}).get('rank') if current_data.get('leaderboard_placement') else None
                    )

            # Parse peak rank
            peak_rank = None
            if 'peak' in mmr_data and mmr_data['peak']:
                peak_data = mmr_data['peak']
                if peak_data and 'tier' in peak_data and peak_data['tier'] and 'season' in peak_data and peak_data['season']:
                    peak_rank = PeakRank(
                        tier_id=peak_data['tier'].get('id', 0),
                        tier_name=peak_data['tier'].get('name', 'Unrated'),
                        season=peak_data['season'].get('short', 'Unknown')
                    )

            return current_rank, peak_rank
            
        except APIError:
            raise
        except Exception as e:
            raise APIError(f"Failed to parse MMR data: {str(e)}")
    
    def get_player_full_info(self, name: str, tag: str, region: str = None, platform: str = None) -> Optional[Player]:
        """
        Get complete player information including account and rank data.
        
        Args:
            name: Player name
            tag: Player tag
            region: Region (optional)
            platform: Platform (optional)
            
        Returns:
            Complete Player object or None if not found
        """
        try:
            # Get account info
            player = self.get_account_info(name, tag, region)
            if not player:
                return None
            
            # Get rank info
            try:
                current_rank, peak_rank = self.get_mmr_info(name, tag, region, platform)
                player.current_rank = current_rank
                player.peak_rank = peak_rank
            except APIError as e:
                # Only show warning for unexpected errors, not for missing rank data
                if "not found" not in str(e).lower():
                    print_warning(f"Could not fetch rank data for {name}#{tag}: {str(e)}")
                # For missing rank data, silently continue with None values
            
            return player
            
        except APIError:
            raise

    def get_match_history(self, name: str, tag: str, region: str = None, size: int = 10) -> List[str]:
        """
        Get recent match IDs for a player.

        Args:
            name: Player name
            tag: Player tag
            region: Region (optional)
            size: Number of matches to retrieve (max 20)

        Returns:
            List of match IDs
        """
        region = region or config.default_region
        size = min(size, 20)  # API limit

        try:
            endpoint = f"/valorant/v3/matches/{region}/{name}/{tag}"
            params = {"size": size}
            data = self._make_request(endpoint, params)

            matches_data = data.get('data', [])
            return [match['metadata']['matchid'] for match in matches_data if 'metadata' in match]

        except APIError:
            raise
        except Exception as e:
            raise APIError(f"Failed to parse match history: {str(e)}")

    def get_recent_matches(self, name: str, tag: str, limit: int = 5, region: str = None) -> List[dict]:
        """
        Get recent matches for a player (raw format for live match checking).

        Args:
            name: Player name
            tag: Player tag
            limit: Number of matches to retrieve (default: 5)
            region: Region to search in

        Returns:
            List of raw match data dictionaries
        """
        region = region or self.default_region

        try:
            url = f"{self.base_url}/valorant/v3/matches/{region}/{name}/{tag}"
            params = {'size': limit}

            response = self._make_request(url, params=params)

            if response and 'data' in response:
                return response['data']
            return []

        except APIError:
            raise
        except Exception as e:
            raise APIError(f"Failed to get recent matches: {str(e)}")

    def get_raw_match_data(self, match_id: str, region: str = None) -> Optional[dict]:
        """
        Get raw match data for live match checking.

        Args:
            match_id: Match ID
            region: Region (optional)

        Returns:
            Raw match data dictionary or None if not found
        """
        region = region or config.default_region

        try:
            endpoint = f"/valorant/v4/match/{region}/{match_id}"
            data = self._make_request(endpoint)

            match_data = data.get('data')
            if not match_data:
                return None

            return match_data

        except APIError:
            raise
        except Exception as e:
            raise APIError(f"Failed to get raw match data: {str(e)}")

    def get_match_details(self, match_id: str, region: str = None) -> Optional[MatchInfo]:
        """
        Get detailed information for a specific match.

        Args:
            match_id: Match ID
            region: Region (optional)

        Returns:
            MatchInfo object or None if not found
        """
        region = region or config.default_region

        try:
            endpoint = f"/valorant/v4/match/{region}/{match_id}"
            data = self._make_request(endpoint)

            match_data = data.get('data')
            if not match_data:
                return None

            metadata = match_data.get('metadata', {})
            players_data = match_data.get('players', [])
            teams_data = match_data.get('teams', [])

            # Parse players
            red_team = []
            blue_team = []

            for player_data in players_data:
                # Get additional rank information for the player
                current_rr = 0
                peak_rank = None

                try:
                    # Fetch detailed rank info for this player
                    player_name = player_data.get('name', '')
                    player_tag = player_data.get('tag', '')
                    if player_name and player_tag:
                        current_rank_info, peak_rank_info = self.get_mmr_info(player_name, player_tag, region)
                        if current_rank_info:
                            current_rr = current_rank_info.rr
                        if peak_rank_info:
                            peak_rank = peak_rank_info
                except Exception as e:
                    # Silently continue if rank data fetch fails
                    print(f"Warning: Could not fetch detailed rank for {player_name}#{player_tag}: {e}")

                match_player = MatchPlayer(
                    puuid=player_data.get('puuid', ''),
                    name=player_data.get('name', ''),
                    tag=player_data.get('tag', ''),
                    team_id=player_data.get('team_id', ''),
                    agent_name=player_data.get('agent', {}).get('name', 'Unknown'),
                    tier_id=player_data.get('tier', {}).get('id', 0),
                    tier_name=player_data.get('tier', {}).get('name', 'Unrated'),
                    account_level=player_data.get('account_level', 0),
                    platform=player_data.get('platform', 'PC'),
                    kills=player_data.get('stats', {}).get('kills', 0),
                    deaths=player_data.get('stats', {}).get('deaths', 0),
                    assists=player_data.get('stats', {}).get('assists', 0),
                    score=player_data.get('stats', {}).get('score', 0),
                    damage_dealt=player_data.get('stats', {}).get('damage', {}).get('dealt', 0),
                    damage_received=player_data.get('stats', {}).get('damage', {}).get('received', 0),
                    current_rr=current_rr,
                    peak_rank=peak_rank
                )

                if player_data.get('team_id') == 'Red':
                    red_team.append(match_player)
                else:
                    blue_team.append(match_player)

            # Parse team results
            red_rounds_won = 0
            blue_rounds_won = 0
            red_team_won = False

            for team in teams_data:
                if team.get('team_id') == 'Red':
                    red_rounds_won = team.get('rounds', {}).get('won', 0)
                    red_team_won = team.get('won', False)
                else:
                    blue_rounds_won = team.get('rounds', {}).get('won', 0)

            # Parse match info
            started_at = datetime.fromisoformat(metadata.get('started_at', '').replace('Z', '+00:00')) if metadata.get('started_at') else datetime.now()

            return MatchInfo(
                match_id=metadata.get('match_id', match_id),
                map_name=metadata.get('map', {}).get('name', 'Unknown') if isinstance(metadata.get('map'), dict) else metadata.get('map', 'Unknown'),
                game_mode=metadata.get('queue', {}).get('name', 'Unknown') if isinstance(metadata.get('queue'), dict) else 'Unknown',
                started_at=started_at,
                game_length_ms=metadata.get('game_length_in_ms', 0),
                region=metadata.get('region', region),
                platform=metadata.get('platform', 'PC'),
                red_team=red_team,
                blue_team=blue_team,
                red_rounds_won=red_rounds_won,
                blue_rounds_won=blue_rounds_won,
                red_team_won=red_team_won
            )

        except APIError:
            raise
        except Exception as e:
            raise APIError(f"Failed to parse match details: {str(e)}")
