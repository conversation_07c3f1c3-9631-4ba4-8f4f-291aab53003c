{% extends "base.html" %}

{% block title %}Batch Player Lookup - Valorant Rank Checker{% endblock %}

{% block content %}
<div class="row justify-content-center">
    <div class="col-lg-10">
        <div class="card">
            <div class="card-header">
                <h3 class="card-title mb-0">
                    <i class="bi bi-people-fill"></i> Batch Player Lookup
                </h3>
            </div>
            <div class="card-body">
                <p class="text-muted mb-4">
                    Enter multiple Riot IDs to check several players at once. Perfect for analyzing entire teams or friend groups.
                </p>
                
                <form action="{{ url_for('batch_lookup_post') }}" method="POST">
                    <div class="mb-3">
                        <label for="riot_ids" class="form-label">Riot IDs</label>
                        <textarea 
                            class="form-control" 
                            id="riot_ids" 
                            name="riot_ids" 
                            rows="6" 
                            placeholder="Enter Riot IDs separated by commas, spaces, or new lines:&#10;&#10;TenZ#SEN&#10;Shroud#C9, s0m#NRG&#10;ScreaM#LIQUID Jamppi#LIQUID"
                            required
                        ></textarea>
                        <div class="form-text text-muted">
                            You can separate Riot IDs with commas, spaces, or put each on a new line. Maximum 10 players per batch.
                        </div>
                    </div>
                    
                    <div class="d-grid">
                        <button type="submit" class="btn btn-primary btn-lg">
                            <i class="bi bi-search"></i> Look Up Players
                        </button>
                    </div>
                </form>
            </div>
        </div>
        
        <div class="row mt-4">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-body">
                        <h5 class="card-title">
                            <i class="bi bi-lightbulb text-warning"></i> Tips
                        </h5>
                        <ul class="mb-0">
                            <li>Use the format: name#tag for each player</li>
                            <li>You can mix different separators (commas, spaces, new lines)</li>
                            <li>Invalid Riot IDs will be skipped</li>
                            <li>Results are limited to 10 players to avoid rate limits</li>
                        </ul>
                    </div>
                </div>
            </div>
            
            <div class="col-md-6">
                <div class="card">
                    <div class="card-body">
                        <h5 class="card-title">
                            <i class="bi bi-clock text-info"></i> Processing Time
                        </h5>
                        <p class="mb-0">
                            Batch lookups may take a few seconds to complete as we fetch data for each player. 
                            Please be patient and avoid refreshing the page.
                        </p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    const form = document.querySelector('form');
    const submitBtn = document.querySelector('button[type="submit"]');
    const originalText = submitBtn.innerHTML;
    
    form.addEventListener('submit', function() {
        submitBtn.innerHTML = '<span class="loading"></span> Processing...';
        submitBtn.disabled = true;
    });
    
    // Reset button if user navigates back
    window.addEventListener('pageshow', function() {
        submitBtn.innerHTML = originalText;
        submitBtn.disabled = false;
    });
});
</script>
{% endblock %}
